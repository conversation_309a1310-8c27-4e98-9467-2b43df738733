<template>
  <div class="common-table-page" style="display: flex; background: #ffffff">
    <div style="flex: 1; position: relative; border-right: 1px solid #e5e6eb">
      <MapBox @onMapMounted="onMapMounted" />
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" :activeStyle="activeStyle" ref="mapStyleRef" />
    </div>
    <div style="flex: 1; display: flex; flex-direction: column; padding: 24px">
      <div class="header">
        选择预报范围
        <a-select v-model="fcstRange" :options="fcstRangeOptions" style="width: 150px; margin: 0 24px 0 16px" />
        当前时间:{{ nowTime }}
      </div>

      <div
        style="
          padding: 20px 0 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #f2f3f5;
        "
      >
        <div style="display: flex; align-items: center">
          <span>选择时间</span>
          <a-date-picker
            class="date-time-picker"
            v-model="startTime"
            format="YYYY-MM-DD HH:mm"
            valueFormat="YYYY-MM-DD HH:00"
            :show-time="{ format: 'HH:mm' }"
            style="min-width: 100px; width: 160px; margin-left: 10px"
          />
          &nbsp;
          <label class="to">至</label>
          &nbsp;
          <a-date-picker
            allow-clear
            class="date-time-picker"
            v-model="endTime"
            format="YYYY-MM-DD HH:mm"
            valueFormat="YYYY-MM-DD HH:00"
            :show-time="{ format: 'HH:mm' }"
            style="min-width: 100px; width: 160px"
          />
        </div>

        <div style="display: flex">
          <a-button icon="search" @click="getList" size="small" type="primary" style="margin-right: 10px">
            查询
          </a-button>
          <a-button icon="reload" @click="reset" size="small">重置</a-button>
        </div>
      </div>

      <div class="summary">
        <hgroup class="hgroup">
          <h5 class="text">
            <i class="icon"></i>
            预报累计降雨量
          </h5>
          <h2 class="num">
            {{ dataSource?.rainSum }}
            <span class="text unit">mm</span>
          </h2>
        </hgroup>
        <hgroup class="hgroup">
          <h5 class="text">
            <i class="icon"></i>
            预计总来水量
          </h5>
          <h2 class="num">
            {{ dataSource?.inWaterSum }}
            <span class="text unit">m³/s</span>
          </h2>
        </hgroup>
        <hgroup class="hgroup">
          <h5 class="text">
            <i class="icon"></i>
            预报最高水位
          </h5>
          <h2 class="num">
            {{ dataSource?.wlvMax === null ? '--' : dataSource?.wlvMax }}
            <span class="text unit">m</span>
          </h2>
        </hgroup>
      </div>
      <div class="flood-box">
        <p class="flood-tabs">
          <label class="name">来水预报</label>
          <span class="tabs">
            <a :class="{ 'tab-active': currentTab == 1 }" @click="currentTab = 1">图表</a>
            <a :class="{ 'tab-active': currentTab == 2 }" @click="currentTab = 2">表格</a>
          </span>
        </p>
        <div class="flood-table-box">
          <p class="top">
            <span>
              上次上报时间
              <span class="last-time">({{ dataSource?.createdTime || '--' }})</span>
            </span>
            <i class="remainder-icon"></i>
            <span>
              距离下次预报剩余时间：
              <span class="next-time">{{ remainingTime }}</span>
            </span>
          </p>

          <ResultChart v-if="currentTab == 1 && !!dataSource" :dataSource="dataSource" :scenario="'forecast'" />

          <ResultTable v-if="currentTab == 2" :dataSource="dataSource?.fcsts || []" :scenario="'forecast'" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { getOptions, getValueByKey } from '@/api/common'
  import { queryAutoForecast } from '../services'
  import MapBox from '@/components/MapBox/index.vue'
  import MapStyle from '@/components/MapBox/MapStyle.vue'
  import moment from 'moment'
  import axios from 'axios'
  import { dealAllPoint, mapBound, mapBoundGeo } from '@/utils/mapBounds.js'
  import { SocketClient } from '@/utils/sockClient.js'
  import ResultChart from './modules/Chart.vue'
  import ResultTable from '../components/ResultTable.vue'

  export default {
    name: 'AutomaticForecast',
    components: { MapBox, MapStyle, ResultChart, ResultTable },

    data() {
      return {
        activeStyle: '卫星图',
        nowTime: null,
        fcstRangeOptions: [],

        mapIns: null,
        fcstRange: undefined,
        startTime: moment().format('YYYY-MM-DD HH:00'),
        endTime: moment().add(72, 'hours').format('YYYY-MM-DD HH:00'),

        dataSource: null,
        currentTab: 1,

        timer: null,
        remainingTime: null,
        predictionSeconds: 0,
        autoPredMinute: 0,
      }
    },
    computed: {},
    created() {
      getOptions('fcstRange').then(res => {
        this.fcstRangeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        this.fcstRange = res.data[0].key

        this.getList()
      })

      getValueByKey('mode.inwater.auto.min').then(res => {
        this.autoPredMinute = Number(res.data)
        this.getAutoPredictionMin()
      })

      this.socketIns = new SocketClient()
      this.socketIns.connect('/topic/model/in-water/auto-fcst-success', response => {
        if (response.data == this.fcstRange) {
          this.getList()
        } else {
          // this.errorInfo = response.message || '模型计算异常了'
        }
      })
    },
    beforeDestroy() {
      clearInterval(this.timer)
      this.timer = null
      this.socketIns.disconnect()
    },
    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns

        // 水库边界
        getValueByKey('gis.thj.reservoir.boundary').then(res => {
          axios(res.data).then(resp => {
            mapBoundGeo(resp.data, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
            mapIns.addLayer({
              id: 'sk-area-fill',
              type: 'fill',
              source: {
                type: 'geojson',
                data: resp.data,
              },
              paint: {
                'fill-color': '#03FFCD',
                'fill-width': 2,
              },
            })
          })
        })
      },

      getList() {
        queryAutoForecast({
          fcstRange: this.fcstRange,
          startTime: this.startTime,
          endTime: this.endTime,
        }).then(res => {
          // this.startTime = res.data.startTime || this.startTime
          // this.endTime = res.data.endTime || this.endTime
          this.dataSource = res.data
        })
      },
      reset() {
        this.startTime = moment().format('YYYY-MM-DD HH:00')
        this.endTime = moment().add(72, 'hours').format('YYYY-MM-DD HH:00')
        this.getList()
      },

      //分钟
      getAutoPredictionMin() {
        this.predictionSeconds = 0
        let m = Number(moment().format('mm'))
        let s = Number(moment().format('ss'))

        let execSeconds = this.autoPredMinute * 60
        let nowSeconds = m * 60 + s
        let nextExecSeconds = nowSeconds > execSeconds ? execSeconds + 3600 : execSeconds
        let diffSeconds = nextExecSeconds - nowSeconds
        const diffDate = moment().subtract(diffSeconds, 'seconds').format('YYYY-MM-DD HH:mm:ss')
        this.predictionSeconds = moment(new Date()).diff(diffDate, 'seconds')
        this.timer = window.setInterval(this.startCountdown, 1002)
      },
      //time
      startCountdown() {
        this.nowTime = moment().format('HH:mm:ss')
        this.predictionSeconds--
        if (this.predictionSeconds == -1) {
          this.timer = null
          clearInterval(this.timer)
          this.getAutoPredictionMin()
        }
        const second = this.predictionSeconds % 60
        const minutes = (this.predictionSeconds - second) / 60
        const minute = minutes % 60
        this.remainingTime = minute + '分' + second + '秒'
      },
    },
  }
</script>

<style lang="less" scoped>
  .header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #e8f3ff;
    border-radius: 4px;
  }

  .summary {
    padding: 15px 0 5px 0;
    display: flex;
    border-bottom: 1px solid #f2f3f5;
    .hgroup {
      // flex: 1;
      width: 31%;
      .num {
        font-weight: 700;
        font-size: 26px;
        color: #1d2129;
        margin-top: -14px;
        padding-left: 6px;
        .time {
          font-size: 18px;
          word-spacing: -2px;
        }
      }
      .text {
        font-size: 14px;
        color: #1d2129;
        font-weight: 400;
      }
      .unit {
        margin-left: -2px;
      }
      .icon {
        width: 30px;
        height: 30px;
        display: inline-block;
        vertical-align: middle;
      }
      &:nth-child(1) {
        .icon {
          background: url('@/assets/images/prediction-icon1.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(2) {
        .icon {
          background: url('@/assets/images/prediction-icon2.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(3) {
        .icon {
          background: url('@/assets/images/prediction-icon3.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
    }
  }

  .flood-box {
    width: 100%;
    flex: 1;
    padding-top: 10px;
    border-top: 1px solid #f2f3f5;
    display: flex;
    flex-direction: column;
    .flood-tabs {
      .name {
        font-size: 20px;
        color: #1d2129;
        font-weight: 600;
      }
      .tabs {
        width: 122px;
        height: 24px;
        background: #f2f3f8;
        display: inline-block;
        margin-left: 10px;
        border-radius: 2px 2px 2px 2px;
        a {
          width: 58px;
          height: 22px;
          display: inline-block;
          text-align: center;
          margin: 1px;
        }
        .tab-active {
          background: #ffffff;
          color: #1664ff;
          border-radius: 2px 2px 2px 2px;
        }
      }
    }
    .flood-table-box {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      .top {
        width: 100%;
        height: 48px;
        padding: 0 10px;
        line-height: 48px;
        background: rgba(229, 230, 235, 0.5);
        border-radius: 6px;
        margin-bottom: 10px;
        .last-time {
          margin-left: 6px;
        }
        .next-time {
          font-weight: 700;
          font-size: 20px;
          color: #1d2129;
        }
      }
      .remainder-icon {
        width: 22px;
        height: 22px;
        display: inline-block;
        vertical-align: middle;
        margin-left: 20px;
        background: url('@/assets/images/remainder-icon.png') 0 0 no-repeat;
        background-size: 100%;
      }
    }
  }

  @font-face {
    font-family: 'AlimamaDaoLiTi';
    src: url('@/assets/font/AlimamaDaoLiTi.ttf');
  }
</style>
