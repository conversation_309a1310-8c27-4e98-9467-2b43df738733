<template>
  <div class="rainfall-container">
    <!-- Tab栏 -->
    <div class="rainfall-tabs">
      <a-tabs v-model="activeTab" @change="handleTabChange" size="small">
        <a-tab-pane key="measured" tab="实测降雨">
        </a-tab-pane>
        <a-tab-pane v-if="baseInfo && baseInfo.scene === 2" key="forecast" tab="未来预报">
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="rainfall-panel">
      <!-- 左侧：降雨统计 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>降雨统计</h3>
        </div>
        
        <!-- 缩放系数和总降雨量 -->
        <div v-if="showScaleSection" class="scale-section">
          <div class="scale-controls">
            <label>缩放系数：</label>
            <a-slider
              v-model="scaleValue"
              :min="0.1"
              :max="5"
              :step="0.1"
              style="width: 150px; margin: 0 10px;"
              @change="handleScaleChange"
            />
            <a-input-number
              v-model="scaleValue"
              :min="0.1"
              :max="5"
              :step="0.1"
              :precision="1"
              size="small"
              style="width: 40px;"
              @change="handleScaleChange"
            />
          </div>
          <div class="total-rainfall-controls">
            <label>总降雨量：</label>
            <a-input-number
              v-model="totalRainfallValue"
              :min="0"
              :step="0.1"
              :precision="1"
              size="small"
              style="width: 100px;"
              @change="handleTotalRainfallChange"
            />
            <span style="margin-left: 4px;">mm</span>
          </div>
        </div>

        <!-- 柱状图 -->
        <div class="chart-section">
          <div ref="chartContainer" :style="chartContainerStyle"></div>
          <!-- 悬浮输入框 -->
          <div
            v-if="hoverTooltip.show"
            :style="{
              position: 'absolute',
              left: hoverTooltip.x + 'px',
              top: hoverTooltip.y + 'px',
              background: 'linear-gradient(135deg, rgba(253, 254, 255, 0.6) 0%, rgba(244, 247, 252, 0.6) 100%)',
              border: '1px solid #E5E7EB',
              borderRadius: '6px',
              padding: '8px 12px',
              fontSize: '12px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              zIndex: 1000,
              transform: 'translateX(-50%)',
              whiteSpace: 'nowrap'
            }"
            class="hover-tooltip"
            @mouseenter="isHoveringTooltip = true"
            @mouseleave="isHoveringTooltip = false"
          >
            <div style="margin-bottom: 4px; color: #666;">{{ hoverTooltip.time }}</div>
            <div v-if="isEditable" style="display: flex; align-items: center; gap: 4px;">
              <span>时段雨量</span>
              <a-input-number
                v-model="hoverTooltip.inputValue"
                size="small"
                :min="0"
                :step="0.1"
                :precision="1"
                style="width: 60px;"
                @change="handleTooltipInputChange"
                @pressEnter="handleTooltipInputChange"
              />
              <span>mm</span>
            </div>
            <div v-else style="display: flex; align-items: center; gap: 4px;">
              <span>时段雨量: {{ hoverTooltip.inputValue }} mm</span>
            </div>
            <div v-if="isEditable" style="margin-top: 4px; font-size: 10px; color: #999;">
              点击柱子Y轴位置可快速设值
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：降雨过程 -->
      <div class="right-panel">
        <div class="panel-header">
          <div class="header-left">
            <h3>降雨过程</h3>
          </div>
          <a-button
            v-if="showBatchImportBtn"
            type="primary"
            @click="handleBatchImport"
            class="batch-import-btn"
          >
            批量导入
          </a-button>
        </div>
        
        <div class="table-section">
          <VxeTable
            v-if="columns.length > 0"
            ref="vxeTableRef"
            :isShowTableHeader="false"
            :isShowSetBtn="false"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :tablePage="false"
            :showFooter="true"
            :footerData="footerData"
            :footer-row-style="{ background: '#F8F8F9', fontWeight: 'bold', fontSize: '14px' }"
          ></VxeTable>
        </div>
      </div>
    </div>
    
    
    <!-- 批量导入弹窗 -->
    <BatchImportModal
      :visible.sync="showBatchImport"
      :timeList="list.map(item => item.tm)"
      @save="handleBatchImportSave"
    />
  </div>
</template>

<script lang="jsx">
  import { getInWaterRange, getRainfallList, createInWaterForecast } from '../../../services'
  import VxeTable from '@/components/VxeTable/index.vue'
  import * as echarts from 'echarts'
  import moment from 'moment'
  import BatchImportModal from './BatchImportModal.vue'
  
  export default {
    name: 'TestList',
    props: ['baseInfo', 'rainfall'],
    components: { VxeTable, BatchImportModal },
    data() {
      return {
        loading: false,
        list: [],
        columns: [],
        scaleValue: 1.0,
        totalRainfallValue: 0, // 总降雨量
        chart: null,
        siteNames: [], // 存储站点名称
        footerData: [], // 表格底部汇总数据
        hoveredRowIndex: -1, // 当前悬浮的表格行索引
        hoverTooltip: {
          show: false,
          x: 0,
          y: 0,
          time: '',
          inputValue: 0,
          barIndex: -1
        },
        crosshairGraphics: [], // 十字线图形元素
        currentMousePos: { x: 0, y: 0 }, // 当前鼠标位置
        showBatchImport: false, // 批量导入弹窗显示状态
        activeTab: 'measured', // 当前激活的Tab，默认为实测降雨
      }
    },
    computed: {
      // 计算各时间段的总降雨量
      timeRainfallData() {
        return this.list.map(item => {
          return {
            time: item.tm,
            rainfall: +(item.totalRainfall * this.scaleValue).toFixed(1)
          }
        })
      },

      // 是否显示批量导入按钮
      showBatchImportBtn() {
        // 历史复演场景：实测降雨可以批量导入
        if (this.baseInfo && this.baseInfo.scene === 1) {
          return this.activeTab === 'measured'
        }
        // 未来预报场景：只有未来预报可以批量导入，实测降雨不可以
        if (this.baseInfo && this.baseInfo.scene === 2) {
          return this.activeTab === 'forecast'
        }
        return false
      },

      // 是否显示缩放系数
      showScaleSection() {
        // 历史复演场景：实测降雨显示缩放系数
        if (this.baseInfo && this.baseInfo.scene === 1) {
          return this.activeTab === 'measured'
        }
        // 未来预报场景：只有未来预报显示缩放系数，实测降雨不显示
        if (this.baseInfo && this.baseInfo.scene === 2) {
          return this.activeTab === 'forecast'
        }
        return false
      },

      // 图表和表格是否可编辑
      isEditable() {
        // 历史复演场景：实测降雨可编辑
        if (this.baseInfo && this.baseInfo.scene === 1) {
          return this.activeTab === 'measured'
        }
        // 未来预报场景：只有未来预报可编辑，实测降雨不可编辑
        if (this.baseInfo && this.baseInfo.scene === 2) {
          return this.activeTab === 'forecast'
        }
        return false
      },

      // 计算当前总降雨量（未缩放）
      currentTotalRainfall() {
        return this.list.reduce((sum, item) => {
          return sum + (item.totalRainfall || 0)
        }, 0)
      },

      // 动态计算图表容器样式
      chartContainerStyle() {
        const dataLength = this.list.length
        let leftValue = 0

        // 根据数据长度计算left值
        // 参考：70条 = -100px，160条 = -300px
        // 使用线性插值计算
        if (dataLength > 0) {
          // 计算斜率：(y2-y1)/(x2-x1) = (-300-(-100))/(160-70) = -200/90 ≈ -2.22
          const slope = -200 / 90
          // 使用点斜式：y - y1 = m(x - x1)，这里用(70, -100)作为参考点
          leftValue = Math.min(0, -100 + slope * (dataLength - 70))

          // 确保不会过度偏移，设置最小值
          leftValue = Math.max(leftValue, -500)
        }

        return {
          width: '100%',
          height: '500px',
          position: 'relative',
          left: `${leftValue}px`
        }
      }
    },
    watch: {
      list: {
        handler() {
          // 更新图表数据
          this.updateChart()
          // 更新表格底部汇总
          this.updateFooterData()
        },
        deep: true,
      },
      scaleValue() {
        this.updateChart()
      },
      baseInfo: {
        handler(newVal, oldVal) {
          // 当baseInfo发生变化时，重新生成数据
          console.log('baseInfo changed:', { newVal, oldVal })

          // 如果场景发生变化，重置Tab到实测降雨
          if (newVal && oldVal && newVal.scene !== oldVal.scene) {
            this.activeTab = 'measured'
          }

          if (newVal && (
            !oldVal ||
            newVal.startTime !== oldVal.startTime ||
            newVal.endTime !== oldVal.endTime ||
            newVal.scene !== oldVal.scene
          )) {
            console.log('Reloading data due to baseInfo change')
            this.loadData()
          }
        },
        deep: true,
        immediate: false
      },
      // 单独监听时间字段的变化
      'baseInfo.startTime': {
        handler(newVal, oldVal) {
          if (newVal !== oldVal && newVal && this.baseInfo.endTime) {
            console.log('startTime changed:', newVal, 'from:', oldVal)
            this.loadData()
          }
        }
      },
      'baseInfo.endTime': {
        handler(newVal, oldVal) {
          if (newVal !== oldVal && newVal && this.baseInfo.startTime) {
            console.log('endTime changed:', newVal, 'from:', oldVal)
            this.loadData()
          }
        }
      },
      // 监听降雨数据变化
      rainfall: {
        handler(newVal, oldVal) {
          console.log('rainfall data changed:', {
            newVal,
            oldVal,
            hasRainfallData: !!(newVal && newVal.rainfallData && newVal.rainfallData.length > 0),
            hasMeasuredData: !!(newVal && newVal.measuredRainfallData && newVal.measuredRainfallData.length > 0)
          })
          if (newVal && (
            (newVal.rainfallData && newVal.rainfallData.length > 0) ||
            (newVal.measuredRainfallData && newVal.measuredRainfallData.length > 0)
          )) {
            // 延迟执行，确保组件已经完全初始化
            this.$nextTick(() => {
              this.generateTestData()
            })
          } else {
            console.warn('降雨数据为空或格式不正确')
          }
        },
        deep: true,
        immediate: true
      }
    },
    mounted() {
      console.log('TestList mounted')
      this.initChart()
      // 如果已经有降雨数据，立即加载
      if (this.rainfall) {
        this.generateTestData()
      }
    },
    activated() {
      // 由于使用了keep-alive，组件激活时重新检查baseInfo
      console.log('TestList activated with baseInfo:', this.baseInfo, 'rainfall:', this.rainfall)
      if (this.rainfall) {
        this.generateTestData()
      }
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
      }
    },
    methods: {
      // 初始化图表
      initChart() {
        this.chart = echarts.init(this.$refs.chartContainer)

         // 图表鼠标移动事件
         this.chart.getZr().on('mousemove', (e) => {
          const pointInPixel = [e.offsetX, e.offsetY]
          const pointInGrid = this.chart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)
          
          // 更新鼠标位置
          this.currentMousePos = { x: e.offsetX, y: e.offsetY }
          
          if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < this.timeRainfallData.length) {
            const barIndex = Math.round(pointInGrid[0])

            // 计算柱子的x坐标
            const barX = this.chart.convertToPixel({ seriesIndex: 0 }, [barIndex, 0])[0]

            // 显示悬浮框
            this.hoverTooltip = {
              show: true,
              x: barX,
              y: 20, // 固定在柱子上方
              time: this.timeRainfallData[barIndex].time,
              inputValue: this.timeRainfallData[barIndex].rainfall,
              barIndex: barIndex
            }

            // 更新十字线
            // this.updateCrosshair(pointInPixel, pointInGrid, barIndex)

            // 根据是否可编辑设置鼠标样式
            this.chart.getZr().dom.style.cursor = this.isEditable ? 'pointer' : 'default'
          } else {
            this.hoverTooltip.show = false
            this.hideCrosshair()
            this.chart.getZr().dom.style.cursor = 'default'
          }
        })

        // 图表鼠标离开事件
        this.chart.getZr().on('globalout', () => {
          // 延迟隐藏，给用户时间操作输入框
          setTimeout(() => {
            if (!this.isHoveringTooltip) {
              this.hoverTooltip.show = false
              this.hideCrosshair()
            }
          }, 100)
        })

         // 图表点击事件 - 支持Y轴方向调整（仅在可编辑模式下）
         this.chart.getZr().on('click', (e) => {
          if (!this.isEditable) return // 不可编辑时不响应点击

          const pointInPixel = [e.offsetX, e.offsetY]
          const pointInGrid = this.chart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)

          if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < this.timeRainfallData.length) {
            const barIndex = Math.round(pointInGrid[0])
            const clickedValue = pointInGrid[1] // Y轴位置对应的数值

            // 确保点击的Y值为正数
            if (clickedValue >= 0) {
              this.handleBarYAxisClick(barIndex, Math.round(clickedValue * 10) / 10)
            }
          }
        })
      },
      
      // 更新图表
      updateChart() {
        console.log('更新图表, chart:', !!this.chart, 'timeRainfallData:', this.timeRainfallData)

        if (!this.chart) {
          console.warn('图表实例不存在')
          return
        }

        if (this.timeRainfallData.length === 0) {
          console.warn('图表数据为空')
          // 清空图表
          this.chart.setOption({
            xAxis: { data: [] },
            series: [{ data: [] }]
          })
          return
        }
        
        // 计算图表所需的最小宽度（每个柱子占用100px）
        const minWidth = this.timeRainfallData.length * 100 + 'px'
        this.$refs.chartContainer.style.minWidth = minWidth
        
        const option = {
          title: {
            left: 'center',
            textStyle: {
              fontSize: 14
            }
          },
          tooltip: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: this.timeRainfallData.map(item => item.time),
            axisLabel: {
              rotate: 45,
              fontSize: 10,
              interval: 0,
              margin: 15
            },
            axisTick: {
              alignWithLabel: true
            },
            axisPointer: {
              show: true,
              type: 'line',
              lineStyle: {
                color: '#999',
                width: 1
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '时段雨量(mm)',
            nameTextStyle: {
              fontSize: 12
            },
            axisPointer: {
              show: true,
              type: 'line',
              lineStyle: {
                color: '#999',
                width: 1
              }
            }
          },
          series: [{
            data: this.timeRainfallData.map(item => item.rainfall),
            type: 'bar',
            itemStyle: {
              color: '#165DFF'
            },
            barWidth: 30,        // 使用更小的固定宽度（像素）
            barGap: '60%'       // 柱子之间的间距设为柱子宽度的80%
          }],
          grid: {
            left: '2%',
            right: '4%',
            bottom: '15%',
            containLabel: true
          }
        }

        // 如果有十字线，添加到配置中
        if (this.crosshairGraphics && this.crosshairGraphics.length > 0) {
          option.graphic = this.crosshairGraphics
        }

        this.chart.setOption(option, true)
        
        // 重新调整图表大小以适应容器
        this.chart.resize()
        
        // 添加鼠标移动事件监听
        this.chart.off('mousemove')
        this.chart.off('mouseout')
        this.chart.on('mousemove', this.handleChartMouseMove)
        this.chart.on('mouseout', this.handleChartMouseOut)
      },

      // 更新指定时间的降雨量
      updateTimeRainfall(timeIndex, newValue) {
        // 直接更新总降雨量
        const actualValue = newValue / this.scaleValue
        this.list[timeIndex].totalRainfall = +actualValue.toFixed(1)
        
        // 按比例更新站点降雨量
        const originalTotal = Object.values(this.list[timeIndex].sitesObj).reduce((sum, val) => {
          return sum + (typeof val === 'number' ? val : 0)
        }, 0)
        
        if (originalTotal > 0) {
          const ratio = actualValue / originalTotal
          Object.keys(this.list[timeIndex].sitesObj).forEach(siteId => {
            if (typeof this.list[timeIndex].sitesObj[siteId] === 'number') {
              this.list[timeIndex].sitesObj[siteId] = +(this.list[timeIndex].sitesObj[siteId] * ratio).toFixed(1)
            }
          })
        }
      },
      // 处理缩放系数变化
      handleScaleChange() {
        // 更新总降雨量显示值
        this.updateTotalRainfallValue()
        this.updateChart()
      },

      // 处理总降雨量变化
      handleTotalRainfallChange(newValue) {
        if (newValue >= 0 && this.currentTotalRainfall > 0) {
          // 根据总降雨量计算新的缩放系数
          const newScale = newValue / this.currentTotalRainfall
          this.scaleValue = +newScale.toFixed(1)
          this.updateChart()
        }
      },

      // 更新总降雨量显示值
      updateTotalRainfallValue() {
        this.totalRainfallValue = +(this.currentTotalRainfall * this.scaleValue).toFixed(1)
      },

      // 处理表格降雨量变化
      handleTableRainfallChange(rowIndex, value) {
        this.list[rowIndex].totalRainfall = value || 0
        
        // 按比例更新站点降雨量
        const originalTotal = Object.values(this.list[rowIndex].sitesObj).reduce((sum, val) => {
          return sum + (typeof val === 'number' ? val : 0)
        }, 0)
        
        if (originalTotal > 0 && value > 0) {
          const ratio = value / originalTotal
          Object.keys(this.list[rowIndex].sitesObj).forEach(siteId => {
            if (typeof this.list[rowIndex].sitesObj[siteId] === 'number') {
              this.list[rowIndex].sitesObj[siteId] = +(this.list[rowIndex].sitesObj[siteId] * ratio).toFixed(1)
            }
          })
        }
        
        // 触发图表更新
        this.updateChart()
      },

      // 处理柱状图Y轴点击
      handleBarYAxisClick(barIndex, clickedValue) {
        // 根据点击的Y轴位置更新降雨量
        this.updateTimeRainfall(barIndex, clickedValue)
        
        // 同时更新悬浮框的值
        if (this.hoverTooltip.show && this.hoverTooltip.barIndex === barIndex) {
          this.hoverTooltip.inputValue = clickedValue
        }
      },

      // 处理悬浮框输入变化
      handleTooltipInputChange() {
        if (this.hoverTooltip.barIndex >= 0 && this.hoverTooltip.inputValue >= 0) {
          this.updateTimeRainfall(this.hoverTooltip.barIndex, this.hoverTooltip.inputValue)
        }
      },

      // 处理表格向下填充
      handleFillDown(fromIndex) {
        const fillValue = this.list[fromIndex].totalRainfall
        
        // 向下填充到所有后续行
        for (let i = fromIndex + 1; i < this.list.length; i++) {
          this.list[i].totalRainfall = fillValue
          
          // 同时更新站点数据
          const originalTotal = Object.values(this.list[i].sitesObj).reduce((sum, val) => {
            return sum + (typeof val === 'number' ? val : 0)
          }, 0)
          
          if (originalTotal > 0 && fillValue > 0) {
            const ratio = fillValue / originalTotal
            Object.keys(this.list[i].sitesObj).forEach(siteId => {
              if (typeof this.list[i].sitesObj[siteId] === 'number') {
                this.list[i].sitesObj[siteId] = +(this.list[i].sitesObj[siteId] * ratio).toFixed(1)
              }
            })
          }
        }
        
        // 更新图表
        this.updateChart()
        
        // 隐藏悬浮行
        this.hoveredRowIndex = -1
      },

      // 更新表格底部汇总数据
      updateFooterData() {
        if (this.list.length === 0) {
          this.footerData = []
          return
        }
        
        const totalRainfall = this.list.reduce((sum, item) => {
          return sum + (item.totalRainfall || 0)
        }, 0)
        
        this.footerData = [{
          totalRainfall: +totalRainfall.toFixed(1)
        }]
      },
      // 批量导入
      handleBatchImport() {
        this.showBatchImport = true
      },
      
      // 批量导入保存
      handleBatchImportSave(importData) {
        // importData: [{time, rainfall}]
        this.list.forEach((item, idx) => {
          const importItem = importData.find(data => data.time === item.tm)
          if (importItem && importItem.rainfall !== undefined) {
            // 更新降雨量
            this.updateTimeRainfall(idx, importItem.rainfall)
          }
        })
        
        // 更新图表和底部汇总
        this.updateChart()
        this.updateFooterData()
        
        this.showBatchImport = false
        this.$message.success('批量导入成功')
      },

      // 加载数据
      loadData() {
        // 注释掉真实接口请求，使用测试数据
        /*
        getInWaterRange({ fcstRange: this.baseInfo.fcstRange }).then(res => {
          // 原接口逻辑
        })
        */

        // 生成测试数据
        this.generateTestData()
      },

      // 生成测试数据
      generateTestData() {
        console.log('generateTestData called with:', {
          baseInfo: this.baseInfo,
          activeTab: this.activeTab,
          rainfall: this.rainfall,
          hasRainfallData: !!(this.rainfall && this.rainfall.rainfallData),
          hasMeasuredData: !!(this.rainfall && this.rainfall.measuredRainfallData)
        })

        // 如果是未来预报场景的实测降雨Tab，使用专门的方法
        if (this.baseInfo && this.baseInfo.scene === 2 && this.activeTab === 'measured') {
          this.loadMeasuredData()
          return
        }

        // 使用接口数据
        this.loadRainfallData()
      },

      // 加载降雨数据
      loadRainfallData() {
        console.log('加载降雨数据, rainfall:', this.rainfall)
        this.loading = true

        // 生成列配置
        this.generateColumns()

        // 使用从Basic页面传递过来的降雨数据
        if (this.rainfall && this.rainfall.rainfallData && this.rainfall.rainfallData.length > 0) {
          console.log('使用rainfallData:', this.rainfall.rainfallData)
          this.processRainfallData(this.rainfall.rainfallData)
        } else {
          console.warn('没有降雨数据或数据为空')
          // 如果没有数据，显示空状态
          this.list = []
          this.loading = false
        }
      },

      // 处理降雨数据
      processRainfallData(rainfallData) {
        console.log('处理降雨数据:', rainfallData)

        if (!rainfallData || !Array.isArray(rainfallData) || rainfallData.length === 0) {
          console.warn('降雨数据为空或格式不正确')
          this.list = []
          this.loading = false
          return
        }

        // 转换接口数据格式为组件需要的格式
        const processedData = rainfallData.map((item, index) => {
          const sitesObj = {}
          let totalRainfall = 0

          // 处理站点数据
          if (item.sites && Array.isArray(item.sites) && item.sites.length > 0) {
            // 如果有多个站点，处理所有站点
            item.sites.forEach(site => {
              const rain = parseFloat(site.rain) || 0
              sitesObj[site.siteId || `site_${index}`] = rain
              totalRainfall += rain
            })
          } else if (item.sites && item.sites.length > 0) {
            // 如果sites不是数组但有数据，取第一项的rain属性
            const firstSite = item.sites[0] || item.sites
            const rain = parseFloat(firstSite.rain) || 0
            sitesObj[firstSite.siteId || `site_${index}`] = rain
            totalRainfall = rain
          } else {
            // 如果没有站点数据，创建默认站点
            console.warn(`时间 ${item.tm} 没有站点数据，创建默认站点`)
            sitesObj[`default_site_${index}`] = 0
            totalRainfall = 0
          }

          return {
            tm: item.tm || `时间_${index}`,
            sitesObj,
            totalRainfall: +totalRainfall.toFixed(1)
          }
        })

        console.log('处理后的数据:', processedData)

        this.list = processedData
        this.loading = false

        // 初始化图表和底部汇总
        this.$nextTick(() => {
          this.updateChart()
          this.updateFooterData()
          this.updateTotalRainfallValue()
        })
      },

      // 加载实测数据（未来预报场景的实测降雨Tab）
      loadMeasuredData() {
        console.log('加载实测数据, rainfall:', this.rainfall)
        this.loading = true

        // 生成列配置
        this.generateColumns()

        // 使用从Basic页面传递过来的实测降雨数据
        if (this.rainfall && this.rainfall.measuredRainfallData && this.rainfall.measuredRainfallData.length > 0) {
          console.log('使用measuredRainfallData:', this.rainfall.measuredRainfallData)
          this.processRainfallData(this.rainfall.measuredRainfallData)
        } else {
          console.warn('没有实测降雨数据或数据为空')
          // 如果没有数据，显示空状态
          this.list = []
          this.loading = false
        }
      },

      async save() {
        // 开始加载状态
        this.$emit('setLoading', true)

        try {
          // 准备降雨数据
          const rains = this.list.map(el => {
            const sites = []
            Object.keys(el.sitesObj).forEach(key => {
              sites.push({ siteId: key, rain: el.sitesObj[key] })
            })
            return { tm: el.tm, sites }
          })

          // 准备请求参数
          const params = {
            caseName: this.baseInfo.caseName,
            startTime: this.baseInfo.startTime,
            endTime: this.baseInfo.endTime,
            scene: this.baseInfo.scene,
            rains
          }

          // 如果是未来预报场景，需要添加实测降雨数据
          if (this.baseInfo.scene === 2 && this.rainfall && this.rainfall.measuredRainfallData) {
            params.futureRains = this.rainfall.measuredRainfallData.map(el => {
              const sites = []
              if (el.sites && Array.isArray(el.sites)) {
                el.sites.forEach(site => {
                  sites.push({ siteId: site.siteId, rain: site.rain })
                })
              }
              return { tm: el.tm, sites }
            })
          }

          // 调用接口
          const response = await createInWaterForecast(params)

          if (response.code === 200) {
            this.$message.success('预报方案创建成功')
            this.$emit('saveData', response.data)
          } else {
            throw new Error(response.message || '创建预报方案失败')
          }
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error(error.message || '保存失败，请重试')
          this.$emit('saveData', false)
        } finally {
          this.$emit('setLoading', false)
        }
      },
      
      // 处理图表鼠标移动事件
      handleChartMouseMove(e) {
        if (!this.chart) return
        
        // 获取鼠标在图表上的位置
        const pos = this.chart.convertFromPixel({ seriesIndex: 0 }, [e.offsetX, e.offsetY])
        const xIndex = Math.round(pos[0])
        
        // 确保索引在有效范围内
        if (xIndex >= 0 && xIndex < this.timeRainfallData.length) {
          // 更新当前鼠标位置
          this.currentMousePos = { x: e.offsetX, y: e.offsetY }
        }
      },
      
      // 处理图表鼠标移出事件
      handleChartMouseOut() {
        if (!this.chart) return

        // 清除十字线
        this.currentMousePos = { x: 0, y: 0 }
      },

      // 处理Tab切换
      handleTabChange(activeKey) {
        this.activeTab = activeKey

        // 根据Tab类型重新生成数据
        this.generateTestData()

        // 如果切换到实测降雨且是未来预报场景，需要生成不可编辑的数据
        if (activeKey === 'measured' && this.baseInfo && this.baseInfo.scene === 2) {
          // 生成历史实测数据（不可编辑）
          this.generateMeasuredData()
        }
      },



      // 生成表格列配置
      generateColumns() {
        const baseColumns = [
          {
            title: '序号',
            field: 'index',
            width: 60,
            slots: {
              default: ({ rowIndex }) => rowIndex + 1,
              footer: () => '总计'
            }
          },
          {
            title: '时间',
            field: 'tm',
            minWidth: 150,
            slots: {
              footer: () => '--'
            }
          }
        ]

        // 根据是否可编辑生成不同的降雨量列
        const rainfallColumn = {
          title: '时段雨量(mm)',
          field: 'totalRainfall',
          minWidth: 200,
          slots: {
            default: ({ row, rowIndex }) => {
              if (this.isEditable) {
                // 可编辑模式
                return (
                  <div
                    class='rainfall-cell'
                    onMouseenter={() => this.hoveredRowIndex = rowIndex}
                    onMouseleave={() => this.hoveredRowIndex = -1}
                    style="display: flex; align-items: center; gap: 8px; width: 100%;"
                  >
                    <a-input-number
                      size='small'
                      step={0.1}
                      min={0}
                      precision={1}
                      v-model={this.list[rowIndex].totalRainfall}
                      onChange={(value) => this.handleTableRainfallChange(rowIndex, value)}
                      style="width: 120px;"
                    />
                    <a
                      style={{
                        color: '#165DFF',
                        textDecoration: 'none',
                        fontSize: '12px',
                        whiteSpace: 'nowrap',
                        opacity: this.hoveredRowIndex === rowIndex ? 1 : 0,
                        transition: 'opacity 0.2s ease',
                        cursor: 'pointer'
                      }}
                      onClick={() => this.handleFillDown(rowIndex)}
                    >
                      向下填充
                    </a>
                  </div>
                )
              } else {
                // 只读模式
                return <span style="color: #666;">{row.totalRainfall} mm</span>
              }
            },
            footer: ({ row }) => {
              return this.footerData.length > 0 ? this.footerData[0].totalRainfall + ' mm' : '0 mm'
            }
          }
        }

        this.columns = [...baseColumns, rainfallColumn]
      },
    }
  }
</script>

<style lang="less" scoped>
  .rainfall-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 20px;
  }

  .rainfall-panel {
    display: flex;
    gap: 10px;
  }

  .left-panel {
    flex: 1.86;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    // padding: 16px;
    overflow: hidden; // 防止内容溢出
  }

  .right-panel {
    flex: 1.4;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    // padding: 16px;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .batch-import-btn {
    border-color: #165DFF;
    color: #165DFF !important;
    background-color: #fff !important;
    // &:hover {
    //   border-color: #3273FF;
    //   color: #3273FF;
    // }
  }

  .scale-section {
    display: flex;
    // flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;

    .scale-controls, .total-rainfall-controls {
      display: flex;
      align-items: center;

      label {
        white-space: nowrap;
        margin-right: 8px;
        font-weight: 500;
        min-width: 80px;
      }
    }

    .total-rainfall-controls {
      label {
        min-width: 80px;
      }
    }
  }

  .chart-section {
    flex: 1;
    min-height: 500px;
    position: relative;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background-color: #F7F8FA;
    overflow-x: auto; // 添加横向滚动
    overflow-y: hidden; // 防止纵向滚动
    
    // 优化滚动条样式
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
      
      &:hover {
        background: #555;
      }
    }
  }

  .hover-tooltip {
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateX(-50%) scale(1.02);
    }
  }

  .rainfall-cell {
    transition: all 0.2s ease;
    
    &:hover {
      background-color: rgba(22, 93, 255, 0.05);
      border-radius: 4px;
      padding: 2px 4px;
      margin: -2px -4px;
    }
    
    a {
      transition: all 0.2s ease;
      
      &:hover {
        color: #3273FF !important;
        text-decoration: underline !important;
      }
    }
  }

  .table-section {
    flex: 1;
    overflow: auto;
  }

  .rainfall-tabs {
    :deep(.ant-tabs-bar) {
      margin-bottom: 0;
      border-bottom: 1px solid #e8e8e8;
    }

    :deep(.ant-tabs-tab) {
      padding: 4px 12px;
      font-size: 13px;

      &.ant-tabs-tab-active {
        color: #165DFF;
        font-weight: 500;
      }
    }

    :deep(.ant-tabs-ink-bar) {
      background-color: #165DFF;
    }

    :deep(.ant-tabs-content) {
      display: none; // 隐藏Tab内容，因为我们只需要Tab标签
    }
  }

  .cell-box {
    a {
      display: none;
      color: #165DFF;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    &:hover {
      a {
        display: inline;
      }
    }
  }
</style>