<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column; background: #ffffff; padding: 24px">
      <!-- 第一大区块 - 基本信息展示 -->
      <div style="margin-bottom: 16px; padding-bottom: 16px; border-bottom: 1px solid #f2f3f5;" v-if="!!dataSource">
        <!-- 上部分信息展示 -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
          <div style="display: flex;">
            <span style="color: #4E5969; font-size: 14px;">方案编码: </span>
            <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ dataSource.caseCode || '暂无数据' }}</span>
          </div>
          <div style="display: flex;">
            <span style="color: #4E5969; font-size: 14px;">预报方式: </span>
            <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ getSourceTypeText(dataSource.sourceType) || '暂无数据' }}</span>
          </div>
          <div style="display: flex;">
            <span style="color: #4E5969; font-size: 14px;">模拟应用场景: </span>
            <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ getSceneText(dataSource.scene) || '暂无数据' }}</span>
          </div>
          <div style="display: flex;">
            <span style="color: #4E5969; font-size: 14px;">预报时段: </span>
            <span style="color: #1D2129; font-size: 14px; margin-right: 16px;">{{ getForecastPeriod(dataSource) || '暂无数据' }}</span>
          </div>
          <div style="display: flex;">
            <span style="color: #4E5969; font-size: 14px;">方案生成时间: </span>
            <span style="color: #1D2129; font-size: 14px;">{{ dataSource.saveTime || '暂无数据' }}</span>
          </div>
        </div>

        <!-- 下部分五块信息 -->
        <div class="summary">
          <hgroup class="hgroup" style="background: #E8F3FF;">
            <i class="icon"></i>
            <div class="content">
              <h5 class="text">前三天实际降雨量</h5>
              <h2 class="num">
                {{ dataSource.beforeRainValue || '0' }}
                <span class="text unit">mm</span>
              </h2>
            </div>
          </hgroup>
          <hgroup class="hgroup" style="background: #E2F6F3;">
            <i class="icon"></i>
            <div class="content">
              <h5 class="text">预报累计降雨量</h5>
              <h2 class="num">
                {{ dataSource.rainSum || '0' }}
                <span class="text unit">mm</span>
              </h2>
            </div>
          </hgroup>
          <hgroup class="hgroup" style="background: #E8EAFF;">
            <i class="icon"></i>
            <div class="content">
              <h5 class="text">预报累计来水量</h5>
              <h2 class="num">
                {{ dataSource.inWaterSum || '0' }}
                <span class="text unit">万m³</span>
              </h2>
            </div>
          </hgroup>
          <hgroup class="hgroup" style="background: #FFF0E8;">
            <i class="icon"></i>
            <div class="content">
              <h5 class="text">洪峰流量</h5>
              <h2 class="num">
                {{ dataSource.maxInflow || '0' }}
                <span class="text unit">m³/s</span>
              </h2>
            </div>
          </hgroup>
          <hgroup class="hgroup" style="background: #E6F5FA;">
            <i class="icon"></i>
            <div class="content">
              <h5 class="text">峰现时间</h5>
              <h2 class="num">
                {{ dataSource.tm || '暂无数据' }}
              </h2>
            </div>
          </hgroup>
        </div>
      </div>

      <!-- 第二大区块 - 图表展示 -->
      <div class="flood-box" style="flex: 1">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <p class="flood-tabs">
            <label class="name">来水预报</label>
          </p>
          <a @click="toggleTable" style="color: #165DFF; cursor: pointer; font-size: 14px;">
            {{ isTableExpanded ? '表格收起' : '表格展开' }}
          </a>
        </div>

        <div class="flood-content" :style="{ display: 'flex', flexDirection: 'row', width: '100%' }">
          <div :style="{ width: isTableExpanded ? '50%' : '100%' }">
            <ResultChart v-if="!!dataSource" :dataSource="mappedDataSource" :scenario="'forecast'" :key="chartKey" />
          </div>
          <div v-if="isTableExpanded" style="width: 50%; margin-left: 16px;">
            <ResultTable :dataSource="mappedDataSource?.fcsts || []" :scenario="'forecast'" :isShowTableHeader="false" />
          </div>
        </div>
      </div>
    </div>
    <template slot="footer">
      <a-button @click="onGenerate">生成水库调度</a-button>
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { getInWater, getInWaterRes } from '../../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import ResultChart from '../../components/Chart.vue'
  import ResultTable from '../../components/ResultTable.vue'
  import { sourceTypeOptions, sceneOptions, modelStatusOptions } from '../config.js'

  export default {
    name: 'FormDrawer',
    components: { AntModal, ResultChart, ResultTable },
    data() {
      return {
        moment,
        sourceTypeOptions,
        sceneOptions,
        modelStatusOptions,
        fcstRangeOptions: [],
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '详情',
        dataSource: null,
        isTableExpanded: true,
        chartKey: 0,
      }
    },
    created() {},
    computed: {
      // 映射数据格式以适配图表和表格组件
      mappedDataSource() {
        if (!this.dataSource) return null;
        return {
          // 表格和图表数据
          reals: this.dataSource.reals || [],
          fcsts: this.dataSource.res || []
        };
      }
    },
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow(row) {
        this.open = true
        this.modalLoading = true

        getOptions('fcstRange').then(res => {
          this.fcstRangeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        })

        getInWaterRes({ inWaterId: row.inWaterId }).then(res => {
          if (res.success && res.data) {
            this.dataSource = res.data
            this.modalTitle = (res.data.caseName || '未命名方案') + '详情'
          } else {
            this.$message.error('获取数据失败')
          }
          this.modalLoading = false
        }).catch(err => {
          console.error('获取详细数据失败:', err)
          this.$message.error('获取数据失败')
          this.modalLoading = false
        })
      },
      onGenerate() {
        this.$emit('handleAddDispatchDetail', this.dataSource)

        this.$nextTick(() => {
          this.open = false
          this.$emit('close')
        })
      },
      // 获取数据源类型文本
      getSourceTypeText(sourceType) {
        const typeMap = {
          1: '自动预报',
          2: '人工预报'
        };
        return typeMap[sourceType] || '未知';
      },
      // 获取场景文本
      getSceneText(scene) {
        const sceneMap = {
          1: '历史复演',
          2: '未来预报'
        };
        return sceneMap[scene] || '未知';
      },
      // 获取预报时段文本
      getForecastPeriod(dataSource) {
        if (!dataSource.startTime || !dataSource.endTime) return '';
        return `${moment(dataSource.startTime).format('YYYY-MM-DD HH')}时 ~ ${moment(dataSource.endTime).format('YYYY-MM-DD HH')}时`;
      },
      // 切换表格显示
      toggleTable() {
        this.isTableExpanded = !this.isTableExpanded;
        this.$nextTick(() => {
          this.chartKey += 1;
        });
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }

  .summary {
    display: flex;
    justify-content: space-between;
    .hgroup {
      width: 19%;
      padding: 24px 26px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;

      .content {
        flex: 1;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .num {
        font-weight: 700;
        font-size: 24px;
        color: #1d2129;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 0;
      }
      .text {
        font-size: 14px;
        color: #4E5969;
        font-weight: 400;
        margin: 0;
      }
      .unit {
        margin-left: -2px;
        font-size: 14px;
      }
      .icon {
        width: 50px;
        height: 50px;
        display: inline-block;
        flex-shrink: 0;
      }
      &:nth-child(1) {
        .icon {
          background: url('@/assets/images/three-days-rain.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(2) {
        .icon {
          background: url('@/assets/images/all-rain.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(3) {
        .icon {
          background: url('@/assets/images/coming-water.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(4) {
        .icon {
          background: url('@/assets/images/flood-peak.png') 0 0 no-repeat;
          background-size: 100%;
        }
      }
      &:nth-child(5) {
        .icon {
          background: url('@/assets/images/flood-peak-time.png') 0 0 no-repeat;
          background-size: 100%;
        }
        .num {
            font-size: 15px;
        }
      }
    }
  }

  .flood-box {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;

    .flood-tabs {
      margin: 0;
      .name {
        font-size: 20px;
        color: #1d2129;
        font-weight: 600;
      }
    }

    .flood-content {
      flex: 1;
    }
  }

  @font-face {
    font-family: 'AlimamaDaoLiTi';
    src: url('@/assets/font/AlimamaDaoLiTi.ttf');
  }
</style>
