<template>
  <div class="container">
    <a-form-model
      style="margin-top: 24px"
      ref="form"
      :model="form"
      :rules="rules"
      layout="horizontal"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 8 },
      }"
    >
      <a-form-model-item label="方案名称" prop="caseName">
        <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
      </a-form-model-item>
      <a-form-model-item label="模型应用场景" prop="scene">
        <a-radio-group
          v-model="form.scene"
          :options="sceneOptions.map(el => ({ ...el, label: `${el.label}场景` }))"
          style="margin-top: 5px"
          @change="changeScene"
        />
      </a-form-model-item>
      <a-form-model-item label="预演时间" prop="forecastTime">
        <div style="display: flex; gap: 8px; align-items: center;">
          <a-select
            v-model="form.forecastTimeType"
            style="width: 120px;"
            data-testid="forecast-time-select"
            @change="handleForecastTimeTypeChange"
          >
            <a-select-option value="1">未来1天</a-select-option>
            <a-select-option value="3">未来3天</a-select-option>
            <a-select-option value="7">未来7天</a-select-option>
            <a-select-option value="custom">自定义</a-select-option>
          </a-select>
          <!-- 预设模式：显示禁用的开始时间和结束时间 -->
          <template v-if="form.forecastTimeType !== 'custom'">
            <a-date-picker
              v-model="form.presetStartTime"
              disabled
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              placeholder="开始时间"
              style="flex: 1;"
            />
            <span style="margin: 0 4px;">至</span>
            <a-date-picker
              v-model="form.presetEndTime"
              disabled
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              placeholder="结束时间"
              style="flex: 1;"
            />
          </template>
          <!-- 自定义模式：显示可编辑的开始时间和结束时间 -->
          <template v-else>
            <a-date-picker
              v-model="form.customStartTime"
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              :disabled-date="disabledCustomStartDate"
              placeholder="开始时间"
              style="flex: 1;"
              @change="handleCustomStartTimeChange"
            />
            <span style="margin: 0 4px;">至</span>
            <a-date-picker
              v-model="form.customEndTime"
              format="YYYY-MM-DD HH:00"
              valueFormat="YYYY-MM-DD HH:00"
              :show-time="{ format: 'HH' }"
              :disabled-date="disabledCustomEndDate"
              placeholder="结束时间"
              style="flex: 1;"
              @change="handleCustomEndTimeChange"
            />
          </template>
        </div>
      </a-form-model-item>
      <!-- <a-form-model-item label="预报范围" prop="fcstRange">
        <a-radio-group v-model="form.fcstRange" :options="fcstRangeOptions" style="margin-top: 5px" />
      </a-form-model-item> -->
    </a-form-model>
  </div>
</template>

<script>
  import moment from 'moment'
  import { getRainfallList, getFutureRainData } from '../../../services'

  export default {
    name: 'BasicInfo',
    props: ['fcstRangeOptions', 'sceneOptions'],
    components: {},
    data() {
      return {
        form: {
          caseName: undefined,
          scene: 2, // 默认选中未来预报场景
          fcstRange: this.fcstRangeOptions[0].value,
          forecastTimeType: '3', // 默认选择未来3天
          presetStartTime: undefined, // 预设模式的开始时间
          presetEndTime: undefined, // 预设模式的结束时间
          customStartTime: undefined, // 自定义模式的开始时间
          customEndTime: undefined, // 自定义模式的结束时间
          startTime: undefined, // 保留用于向后兼容
          endTime: undefined, // 保留用于向后兼容
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          scene: [{ required: true, message: '模型应用场景不能为空', trigger: 'change' }],
          fcstRange: [{ required: true, message: '预报范围不能为空', trigger: 'change' }],
          forecastTime: [
            {
              validator: (rule, value, callback) => {
                if (this.form.forecastTimeType === 'custom') {
                  if (!this.form.customStartTime || !this.form.customEndTime) {
                    callback(new Error('请选择完整的时间区间'))
                  } else {
                    // 校验结束时间必须晚于开始时间
                    const startTime = moment(this.form.customStartTime)
                    const endTime = moment(this.form.customEndTime)
                    if (endTime.isSameOrBefore(startTime)) {
                      callback(new Error('结束时间必须晚于开始时间'))
                    } else {
                      callback()
                    }
                  }
                } else {
                  if (!this.form.presetStartTime || !this.form.presetEndTime) {
                    callback(new Error('预演时间不能为空'))
                  } else {
                    callback()
                  }
                }
              },
              trigger: 'change'
            }
          ],
        },
      }
    },
    computed: {},
    created() {
      // 由于默认选中未来预报场景，需要初始化时间
      if (this.form.scene === 2) {
        this.initializeForecastTime()
      }
    },
    methods: {
      changeScene(val) {
        if (val.target.value === 2) {
          // 切换到未来预报场景
          this.initializeForecastTime()
          this.$refs.form.validateField('forecastTime')
        } else {
          // 切换到历史复演场景，保持当前时间和时间类型不变
          // 如果当前是预设模式，保持预设模式但调整时间范围适合历史场景
          if (this.form.forecastTimeType !== 'custom' && this.form.presetStartTime && this.form.presetEndTime) {
            // 保持预设模式，但调整为历史时间范围
            this.initializeHistoryPresetTime()
          }
          // 如果当前是自定义模式，保持自定义模式
          else if (this.form.forecastTimeType === 'custom') {
            // 如果有自定义时间，保持不变
            if (this.form.customStartTime && this.form.customEndTime) {
              this.form.startTime = this.form.customStartTime
              this.form.endTime = this.form.customEndTime
            }
            // 如果没有自定义时间，初始化历史复演的默认时间
            else {
              this.initializeHistoryTime()
            }
          }
          // 如果都没有，初始化历史复演的默认时间
          else {
            this.initializeHistoryTime()
          }

          this.$refs.form.validateField('forecastTime')
        }
      },

      // 初始化预演时间
      initializeForecastTime() {
        // 开始时间为当前时间的下一个整点
        const now = moment()
        const startTime = now.clone().add(1, 'hours').startOf('hour')

        let endTime
        const days = parseInt(this.form.forecastTimeType) || 3

        // 结束时间 = 开始时间 + 指定天数，但保持相同的小时
        endTime = startTime.clone().add(days, 'days').subtract(1, 'hours')

        this.form.presetStartTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.presetEndTime = endTime.format('YYYY-MM-DD HH:00')

        // 同时更新startTime和endTime用于向后兼容
        this.form.startTime = this.form.presetStartTime
        this.form.endTime = this.form.presetEndTime
      },

      // 处理预演时间类型变化
      handleForecastTimeTypeChange(value) {
        if (value !== 'custom') {
          // 切换到预设模式，清空自定义时间并初始化预设时间
          this.form.customStartTime = undefined
          this.form.customEndTime = undefined
          this.initializeForecastTime()
        } else {
          // 切换到自定义模式，清空预设时间并初始化自定义时间
          this.form.presetStartTime = undefined
          this.form.presetEndTime = undefined
          this.initializeCustomTime()
        }
        this.$refs.form.validateField('forecastTime')
      },

      // 初始化自定义时间
      initializeCustomTime() {
        // 开始时间为当前时间的下一个整点
        const now = moment()
        const startTime = now.clone().add(1, 'hours').startOf('hour')
        const endTime = startTime.clone().add(3, 'days').subtract(1, 'hours')

        this.form.customStartTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.customEndTime = endTime.format('YYYY-MM-DD HH:00')
      },

      // 初始化历史复演时间
      initializeHistoryTime() {
        // 历史复演场景：默认选择过去3天到当前时间
        const now = moment()
        const endTime = now.clone().startOf('hour')
        const startTime = endTime.clone().subtract(3, 'days')

        this.form.customStartTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.customEndTime = endTime.format('YYYY-MM-DD HH:00')
        this.form.startTime = this.form.customStartTime
        this.form.endTime = this.form.customEndTime
      },

      // 初始化历史复演预设时间
      initializeHistoryPresetTime() {
        // 历史复演场景：根据当前预设类型调整时间范围
        const now = moment()
        const endTime = now.clone().startOf('hour')
        const days = parseInt(this.form.forecastTimeType) || 3
        const startTime = endTime.clone().subtract(days, 'days')

        this.form.presetStartTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.presetEndTime = endTime.format('YYYY-MM-DD HH:00')
        this.form.startTime = this.form.presetStartTime
        this.form.endTime = this.form.presetEndTime
      },

      // 处理自定义开始时间变化
      handleCustomStartTimeChange() {
        // 当开始时间变化时，重新验证时间区间
        this.$nextTick(() => {
          this.$refs.form.validateField('forecastTime')
        })
      },

      // 处理自定义结束时间变化
      handleCustomEndTimeChange() {
        // 当结束时间变化时，重新验证时间区间
        this.$nextTick(() => {
          this.$refs.form.validateField('forecastTime')
        })
      },

      // 自定义开始时间日期禁用逻辑
      disabledCustomStartDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：可选择过去15天到今天
          return current < moment().subtract(15, 'days').startOf('day') || current > moment().endOf('day')
        } else {
          // 未来预报场景：可选择今天到未来10天（不限制结束时间，在验证时检查）
          return (
            current < moment().startOf('day') ||
            current > moment().add(10, 'days').endOf('day')
          )
        }
      },

      // 自定义结束时间日期禁用逻辑
      disabledCustomEndDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：可选择过去15天到今天
          return current < moment().subtract(15, 'days').startOf('day') || current > moment().endOf('day')
        } else {
          // 未来预报场景：可选择今天到未来10天（不限制开始时间，在验证时检查）
          return (
            current < moment().startOf('day') ||
            current > moment().add(10, 'days').endOf('day')
          )
        }
      },
      // 预演时间日期禁用逻辑
      disabledForecastDate(current) {
        if (this.form.scene === 1) {
          // 历史场景：可选择过去15天到今天
          return current < moment().subtract(15, 'days').startOf('day') || current > moment().endOf('day')
        } else {
          // 未来预报场景：可选择今天到未来10天
          return current < moment().startOf('day') || current > moment().add(10, 'days').endOf('day')
        }
      },

      disabledStartDate(current) {
        if (this.form.scene === 1) {
          if (this.form.endTime) {
            return (
              current < moment(this.form.endTime).subtract(15, 'days') ||
              current > moment(this.form.endTime) ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          // 未来预报场景：开始时间虽然被禁用，但逻辑上应该可选择今天及以后
          return current < moment().startOf('day')
        }
      },
      disabledEndDate(current) {
        if (this.form.scene === 1) {
          if (this.form.startTime) {
            return (
              current < moment(this.form.startTime) ||
              current > moment(this.form.startTime).add(15, 'days') ||
              current > moment()
            )
          }
          return current && current > moment()
        } else {
          // 未来预报场景：可选择今天到今天+3天
          return current < moment().startOf('day') || current > moment().add(3, 'days').endOf('day')
        }
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            let startTime, endTime

            if (this.form.forecastTimeType === 'custom') {
              // 自定义模式：使用用户选择的开始时间和结束时间
              startTime = this.form.customStartTime
              endTime = this.form.customEndTime
            } else {
              // 预设模式：使用计算出的开始时间和结束时间
              startTime = this.form.presetStartTime
              endTime = this.form.presetEndTime
            }

            // 构造传递给下一步的数据
            const formData = {
              ...this.form,
              startTime,
              endTime,
              // 添加预演时间区间信息
              forecastTimeRange: {
                type: this.form.forecastTimeType,
                startTime,
                endTime
              }
            }

            // 调用接口获取降雨数据
            this.loadRainfallData(formData)
          } else {
            this.$emit('saveData', false)
          }
        })
      },

      // 加载降雨数据
      async loadRainfallData(formData) {
        try {
          // 调用降雨数据接口
          const rainfallParams = {
            startTime: formData.startTime,
            endTime: formData.endTime,
            scene: formData.scene
          }

          const rainfallRes = await getRainfallList(rainfallParams)

          // 将降雨数据添加到formData中
          formData.rainfallData = rainfallRes.data || []

          // 如果是未来预报场景，还需要获取实测降雨数据
          if (formData.scene === 2) {
            const futureRainRes = await getFutureRainData(rainfallParams)
            formData.measuredRainfallData = futureRainRes.data || []
          }

          this.$emit('saveData', formData)
        } catch (error) {
          console.error('获取降雨数据失败:', error)
          this.$message.error('获取降雨数据失败，请重试')
          this.$emit('saveData', false)
        }
      },
    },
  }
</script>

<style lang="less" scoped></style>
